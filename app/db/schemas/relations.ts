import { relations } from "drizzle-orm";

import { accounts } from "./account";
import { accountGroups } from "./account_group";
import { budgets } from "./budget";
import { budgetRecords } from "./budget_record";
import { categories } from "./category";
import { transactions } from "./transaction";
import { users } from "./user";

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, { fields: [accounts.userId], references: [users.id] }),
  group: one(accountGroups, { fields: [accounts.groupId], references: [accountGroups.id] }),
}));

export const accountGroupsRelations = relations(accountGroups, ({ one }) => ({
  user: one(users, { fields: [accountGroups.userId], references: [users.id] }),
}));

export const categoriesRelations = relations(categories, ({ one }) => ({
  user: one(users, { fields: [categories.userId], references: [users.id] }),
}));

export const transactionsRelations = relations(transactions, ({ one }) => ({
  user: one(users, { fields: [transactions.userId], references: [users.id] }),
  account: one(accounts, { fields: [transactions.accountId], references: [accounts.id] }),
  accountTo: one(accounts, { fields: [transactions.accountToId], references: [accounts.id] }),
  category: one(categories, { fields: [transactions.categoryId], references: [categories.id] }),
}));

export const budgetsRelations = relations(budgets, ({ one, many }) => ({
  user: one(users, { fields: [budgets.userId], references: [users.id] }),
  records: many(budgetRecords),
}));

export const budgetRecordsRelations = relations(budgetRecords, ({ one }) => ({
  budget: one(budgets, { fields: [budgetRecords.budgetId], references: [budgets.id] }),
}));

export const usersRelations = relations(users, ({ many }) => ({
  accounts: many(accounts),
  accountGroups: many(accountGroups),
  categories: many(categories),
  transactions: many(transactions),
}));
